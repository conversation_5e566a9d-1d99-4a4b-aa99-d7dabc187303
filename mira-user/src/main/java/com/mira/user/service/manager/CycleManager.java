package com.mira.user.service.manager;

import com.mira.api.bluetooth.dto.algorithm.AlgorithmResultDTO;
import com.mira.api.bluetooth.dto.cycle.*;
import com.mira.api.bluetooth.dto.menopause.MenopauseResultDTO;
import com.mira.api.bluetooth.enums.AlgorithmRequestTypeEnum;
import com.mira.api.bluetooth.enums.CycleStatusEnum;
import com.mira.api.bluetooth.provider.IMenopauseProvider;
import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.user.enums.UserGoalEnum;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.DaySuffixUtil;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.user.controller.vo.chart.PregnantRiskVO;
import com.mira.user.controller.vo.cycle.CycleAnalysisVO;
import com.mira.user.dal.dao.AppUserPeriodDAO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * 周期接口通用层
 *
 * <AUTHOR>
 */
@Component
public class CycleManager {
    @Resource
    private IMenopauseProvider menopauseProvider;
    @Resource
    private AppUserPeriodDAO appUserPeriodDAO;

    public CycleAnalysisVO buildCycleAnalysisVO(LoginUserInfoDTO loginUserInfoDTO,
                                                AlgorithmResultDTO algorithmResultDTO,
                                                List<Long> sexDateList) {
        CycleAnalysisDTO cycleAnalysis = JsonUtil.toObject(algorithmResultDTO.getCycleAnalysis(), CycleAnalysisDTO.class);
        List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(algorithmResultDTO.getCycleData(), CycleDataDTO.class);
        List<HormoneDTO> hormoneDatas = JsonUtil.toArray(algorithmResultDTO.getHormoneData(), HormoneDTO.class);

        Integer ttaSwitch = loginUserInfoDTO.getTtaSwitch();
        Integer goalStatus = loginUserInfoDTO.getGoalStatus();
        Integer userMode = UserGoalEnum.getUserMode(ttaSwitch, goalStatus);
        Integer trackingMenopause = loginUserInfoDTO.getTrackingMenopause();

        return buildCycleAnalysisVO(loginUserInfoDTO.getUserId(), userMode, trackingMenopause, cycleAnalysis,
                cycleDataDTOS, hormoneDatas, sexDateList);
    }

    public CycleAnalysisVO buildCycleAnalysisVO(Long userId,
                                                Integer userMode,
                                                Integer trackingMenopause,
                                                CycleAnalysisDTO cycleAnalysis,
                                                List<CycleDataDTO> cycleDataDTOS,
                                                List<HormoneDTO> hormoneDatas,
                                                List<Long> sexDateList) {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        CycleAnalysisVO cycleAnalysisVO = new CycleAnalysisVO();
        cycleAnalysisVO.setUserMode(userMode);
        cycleAnalysisVO.setTrackingMenopause(trackingMenopause == null ? 0 : trackingMenopause);
        if (trackingMenopause != null && trackingMenopause == 1) {
            MenopauseResultDTO menopauseResultDTO = menopauseProvider.getMenopauseResult(userId,
                    AlgorithmRequestTypeEnum.GET_MENOPAUSE_RESULT_buildCycleAnalysisVO).getData();
            cycleAnalysisVO.setMenopauseStage(menopauseResultDTO.getDefineStage());

            HormoneDTO lastFshHormoneDTO = hormoneDatas.stream()
                                                       .filter(hormoneDTO -> WandTypeEnum.FSH.getInteger().equals(hormoneDTO.getTest_results().getWand_type()))
                                                       .max((Comparator.comparing(HormoneDTO::getTest_time)))
                                                       .orElse(null);
            if (lastFshHormoneDTO != null) {
                TestDataDTO lastFsh = new TestDataDTO();
                lastFsh.setTestTime(lastFshHormoneDTO.getTest_time());
                lastFsh.setValue(lastFshHormoneDTO.getTest_results().getValue1());
                cycleAnalysisVO.setLastFsh(lastFsh);
            }
            String lastPeriodDate = appUserPeriodDAO.getLastPeriodDate(userId, timeZone);
            cycleAnalysisVO.setLastMenstrualPeriodDate(lastPeriodDate);

        }

        if (cycleAnalysis != null) {
            cycleAnalysisVO.setCycleLength(cycleAnalysis.getCycle_len());
            cycleAnalysisVO.setPeriodLength(cycleAnalysis.getPeriod_len());
            cycleAnalysisVO.setLutealPhases(cycleAnalysis.getLuteal_phases());
            cycleAnalysisVO.setFolicularPhases(cycleAnalysis.getFolicular_phases());
            Integer ovulationEstimate = cycleAnalysis.getOvulation_estimate();
            cycleAnalysisVO.setOvulationEstimate(ovulationEstimate);
            if (ovulationEstimate != null) {
                cycleAnalysisVO.setOvulationEstimateUnit(DaySuffixUtil.getDaySuffix(ovulationEstimate));
            }
        }

        List<CycleAnalysisVO.CycleData> analysisCycleData = new ArrayList<>();

        for (CycleDataDTO cycleDataDTO : cycleDataDTOS) {
            if (CycleStatusEnum.REAL_CYCLE.getStatus() != cycleDataDTO.getCycle_status()) {
                continue;
            }
            CycleAnalysisVO.CycleData cycleDataVO = new CycleAnalysisVO.CycleData();
            cycleDataVO.setCycleIndex(cycleDataDTO.getCycle_index());
            cycleDataVO.setLenCycle(cycleDataDTO.getLen_cycle());

            String date_period_start = cycleDataDTO.getDate_period_start();
            String date_period_end = cycleDataDTO.getDate_period_end();
            String date_FW_start = cycleDataDTO.getDate_FW_start();
            String date_FW_end = cycleDataDTO.getDate_FW_end();
            String date_LH_surge = cycleDataDTO.getDate_LH_surge();
            List<String> date_pdg_rises = cycleDataDTO.getDate_PDG_rise();

            if (StringUtils.isBlank(date_period_start) || StringUtils.isBlank(date_period_end)) {
                continue;
            }
            if (StringUtils.isBlank(date_FW_start) || StringUtils.isBlank(date_FW_end)) {
                continue;
            }

            Long date_period_start_l = ZoneDateUtil.timestamp(timeZone, date_period_start, DatePatternConst.DATE_PATTERN);

            cycleDataVO.setDatePeriodStart(date_period_start);
            cycleDataVO.setIndexPeriodEnd(LocalDateUtil.minusToDay(date_period_end, date_period_start));
            cycleDataVO.setIndexFwStart(LocalDateUtil.minusToDay(date_FW_start, date_period_start));
            cycleDataVO.setIndexFwEnd(LocalDateUtil.minusToDay(date_FW_end, date_period_start));
            if (StringUtils.isNotBlank(date_LH_surge)) {
                cycleDataVO.setIndexLhSurgeDay(LocalDateUtil.minusToDay(date_LH_surge, date_period_start));
            } else {
                cycleDataVO.setIndexLhSurgeDay(null);
            }
            List<Integer> indexPdgRises = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(date_pdg_rises)) {
                for (String date_pdg_rise : date_pdg_rises) {
                    int indexPdgRise = LocalDateUtil.minusToDay(date_pdg_rise, date_period_start);
                    indexPdgRises.add(indexPdgRise);
                }
            }
            cycleDataVO.setIndexPdgRises(indexPdgRises);
            // sex_day index
            List<Integer> sexIndexList = new ArrayList<>();
            if (!sexDateList.isEmpty()) {
                sexDateList.stream().forEach(sexDate -> {
                    long betweenDays = (sexDate - date_period_start_l) / (1000 * 3600 * 24);
                    if (betweenDays >= 0 && betweenDays < cycleDataDTO.getLen_cycle()) {
                        int index = new Long(betweenDays).intValue();
                        sexIndexList.add(index);
                    }
                });
            }
            cycleDataVO.setIndexesOfSex(sexIndexList);

            PregnantRiskVO pregnantRisk = new PregnantRiskVO();
            PregnantRiskDTO pregnant_risk = cycleDataDTO.getPregnant_risk();
            if (pregnant_risk != null) {
                pregnantRisk.setHighRiskCDs(pregnant_risk.getHigh_risks());
                pregnantRisk.setMediumRiskCDs(pregnant_risk.getMedium_risks());
                pregnantRisk.setLowRiskCDs(pregnant_risk.getLow_risks());
            }
            cycleDataVO.setPregnantRisk(pregnantRisk);

            analysisCycleData.add(cycleDataVO);

        }
        if (CollectionUtils.isNotEmpty(analysisCycleData)) {
            Collections.reverse(analysisCycleData);
        }
        cycleAnalysisVO.setCycleData(analysisCycleData);

        return cycleAnalysisVO;
    }
}
